/* Space Animations and Effects */

/* Nebula Background */
.nebula-bg {
  position: absolute;
  inset: 0;
  background-image: 
    radial-gradient(circle at 30% 50%, rgba(76, 29, 149, 0.2) 0%, rgba(30, 64, 175, 0) 70%),
    radial-gradient(circle at 70% 30%, rgba(147, 51, 234, 0.2) 0%, rgba(30, 64, 175, 0) 60%),
    radial-gradient(circle at 50% 80%, rgba(6, 182, 212, 0.2) 0%, rgba(30, 64, 175, 0) 70%);
  filter: blur(40px);
  opacity: 0.6;
  mix-blend-mode: screen;
}

/* Star Twinkling Animation */
@keyframes twinkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

.star {
  position: absolute;
  border-radius: 50%;
  background-color: white;
}

.stars-small, .stars-medium, .stars-large {
  position: absolute;
  inset: 0;
  z-index: 0;
}

.stars-small .star {
  width: 1px;
  height: 1px;
  animation: twinkle 3s infinite ease-in-out;
}

.stars-medium .star {
  width: 2px;
  height: 2px;
  animation: twinkle 5s infinite ease-in-out;
}

.stars-large .star {
  width: 3px;
  height: 3px;
  animation: twinkle 7s infinite ease-in-out;
}

/* Planet Glow Effect */
.planet-glow {
  position: absolute;
  border-radius: 50%;
  filter: blur(20px);
  opacity: 0.5;
  mix-blend-mode: screen;
}

/* Orbit Rings */
.orbit-ring {
  position: absolute;
  border-radius: 50%;
  border: 1px solid rgba(147, 51, 234, 0.2);
  transform-origin: center;
}

/* Shooting Star Animation */
@keyframes shootingStar {
  0% {
    transform: translateX(0) translateY(0) rotate(45deg);
    opacity: 1;
    width: 0;
  }
  70% {
    width: 100px;
    opacity: 1;
  }
  100% {
    transform: translateX(200px) translateY(-200px) rotate(45deg);
    opacity: 0;
    width: 0;
  }
}

.shooting-star {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
  animation: shootingStar 3s ease-in-out infinite;
  animation-delay: calc(var(--delay) * 1s);
  transform-origin: left;
  opacity: 0;
}

/* Parallax Star Field */
.parallax-stars {
  position: fixed;
  inset: 0;
  z-index: 0;
}

.parallax-stars div {
  position: absolute;
  border-radius: 50%;
  background-color: white;
  will-change: transform;
}

/* Floating Objects Animation */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20px); }
}

.floating-object {
  animation: float 6s ease-in-out infinite;
}

.floating-object-slow {
  animation: float 10s ease-in-out infinite;
}

.floating-object-fast {
  animation: float 4s ease-in-out infinite;
}

/* No scrollbar utility */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Cosmic text gradients */
.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-600;
}

/* Card hover effects */
.cosmic-card {
  transition: all 0.3s ease;
}

.cosmic-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(76, 29, 149, 0.2), 0 10px 10px -5px rgba(76, 29, 149, 0.1);
}

/* Planet rotation */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.planet-rotate {
  animation: rotate 40s linear infinite;
}

.planet-rotate-reverse {
  animation: rotate 30s linear infinite reverse;
}

/* Space dust particles */
.space-dust {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
  top: 0;
  left: 0;
}

.space-dust div {
  position: absolute;
  display: block;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

/* Glowing button effect */
.btn-glow {
  position: relative;
  overflow: hidden;
}

.btn-glow::after {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, 
    rgba(56, 189, 248, 0) 0%, 
    rgba(56, 189, 248, 0.3) 50%, 
    rgba(56, 189, 248, 0) 100%);
  z-index: -1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
} 