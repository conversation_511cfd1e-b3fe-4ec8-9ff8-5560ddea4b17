@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
body {
    @apply bg-white text-gray-900 font-inter;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-orbitron;
  }
}

@layer components {
  .section-container {
    @apply container mx-auto px-4 py-16 md:py-24;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-electric-violet to-accent-neon bg-clip-text text-transparent;
  }

  .card-hover {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-lg;
  }
}

/* Custom Animations */
.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}
