import React, { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion, useScroll, useTransform, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Player } from '@lottiefiles/react-lottie-player';
import FloatingContactButton from '../components/FloatingContactButton';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

const ServicesPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const lottieRef = useRef<HTMLDivElement>(null);

  // Scroll-based animations
  const { scrollYProgress } = useScroll();
  const backgroundY = useTransform(scrollYProgress, [0, 1], ['0%', '50%']);
  const heroScale = useTransform(scrollYProgress, [0, 0.5], [1, 1.1]);

  // Services data with Lottie animations
  const services = [
    {
      title: "Transparent Display Technology",
      subtitle: "CrystalView",
      description: "Next-generation transparent displays that integrate seamlessly into environments—from automotive HUDs to smart glass for retail and healthcare.",
      lottieUrl: "https://lottie.host/4d5c8b8e-8f4a-4c8a-9c8d-2e5f6a7b8c9d/abcdef123456.json",
      bgGradient: "from-accent-neon to-cool-blue",
      link: "/transparent-display",
      delay: 0,
    },
    {
      title: "Student Freelancing Platform",
      subtitle: "Script & Style",
      description: "A dynamic freelancing ecosystem built exclusively for college students. Create personal websites, collaborate on real projects, and gain valuable industry experience.",
      lottieUrl: "https://lottie.host/embed/a4f2c8d1-3b5e-4f7a-8c9d-1e2f3a4b5c6d/abcdef123456.json",
      bgGradient: "from-cool-blue to-electric-violet",
      link: "/scriptstyle",
      delay: 0.2,
    },
    {
      title: "Innovation Hub",
      subtitle: "Ideation Lab",
      description: "Our innovation hub focused on pioneering solutions in healthcare and space exploration, bringing together experts from diverse backgrounds.",
      lottieUrl: "https://lottie.host/embed/b5g3d9e2-4c6f-5g8b-9d0e-2f3g4b5c6d7e/abcdef123456.json",
      bgGradient: "from-electric-violet to-indigo-800",
      link: "/ideation-lab",
      delay: 0.4,
    }
  ];

  // GSAP Hero animations
  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 2000);

    if (!isLoading && heroRef.current && titleRef.current && subtitleRef.current) {
      const tl = gsap.timeline();

      // Hero background parallax
      gsap.to(heroRef.current, {
        backgroundPosition: "50% 100%",
        ease: "none",
        scrollTrigger: {
          trigger: heroRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });

      // Title reveal animation
      tl.fromTo(titleRef.current,
        {
          opacity: 0,
          y: 100,
          scale: 0.8
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1.2,
          ease: "power3.out"
        }
      )
      .fromTo(subtitleRef.current,
        {
          opacity: 0,
          y: 50
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power2.out"
        },
        "-=0.6"
      );

      // Lottie container animation
      if (lottieRef.current) {
        gsap.fromTo(lottieRef.current,
          {
            opacity: 0,
            scale: 0.5,
            rotation: -10
          },
          {
            opacity: 1,
            scale: 1,
            rotation: 0,
            duration: 1,
            ease: "back.out(1.7)",
            delay: 0.5
          }
        );
      }
    }

    return () => clearTimeout(timer);
  }, [isLoading]);

  // Loading screen
  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-gray-900 flex items-center justify-center z-50">
        <div className="text-center">
          <Player
            autoplay
            loop
            src="https://lottie.host/embed/a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6/loading.json"
            style={{ width: '200px', height: '200px' }}
          />
          <motion.p
            className="text-white text-xl font-orbitron mt-4"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            Loading Services...
          </motion.p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 overflow-hidden">
      {/* Hero Section with GSAP animations */}
      <motion.section
        ref={heroRef}
        className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-indigo-900 to-purple-900"
        style={{
          backgroundImage: `radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                           radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                           radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%)`,
          y: backgroundY
        }}
      >
        {/* Animated background particles */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-accent-neon rounded-full animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-electric-violet rounded-full animate-ping"></div>
          <div className="absolute top-1/2 left-3/4 w-3 h-3 bg-cool-blue rounded-full animate-bounce"></div>
        </div>

        {/* Hero Content */}
        <div className="container mx-auto px-4 text-center relative z-10">
          <motion.div
            ref={lottieRef}
            className="mb-8 flex justify-center"
            style={{ scale: heroScale }}
          >
            <Player
              autoplay
              loop
              src="https://lottie.host/embed/services-hero.json"
              style={{ width: '300px', height: '300px' }}
            />
          </motion.div>

          <h1
            ref={titleRef}
            className="text-5xl md:text-7xl lg:text-8xl font-bold mb-6 font-orbitron text-transparent bg-clip-text bg-gradient-to-r from-accent-neon via-electric-violet to-cool-blue"
          >
            Our Services
          </h1>

          <p
            ref={subtitleRef}
            className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed"
          >
            Explore our multi-domain solutions across transparent display technology, student freelancing, and healthcare/space innovation.
          </p>

          {/* Scroll indicator */}
          <motion.div
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
              <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Services Grid Section */}
      <section className="py-20 bg-gray-900 relative">
        {/* Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-indigo-900/10 to-transparent"></div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 font-orbitron text-white">
              Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-accent-neon to-electric-violet">Solutions</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Cutting-edge technology meets innovative thinking
            </p>
          </motion.div>

          {/* Services Grid with Glassmorphism */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {services.map((service, index) => (
              <motion.div
                key={service.title}
                className="group relative"
                initial={{ opacity: 0, y: 60, rotateX: 15 }}
                whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
                viewport={{ once: true }}
                transition={{
                  duration: 0.8,
                  delay: service.delay,
                  type: "spring",
                  bounce: 0.4
                }}
                whileHover={{
                  y: -15,
                  rotateY: 5,
                  transition: { duration: 0.3 }
                }}
              >
                <Link to={service.link} className="block h-full">
                  {/* Glassmorphism Card */}
                  <div className="relative h-full bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8 overflow-hidden group-hover:bg-white/10 transition-all duration-500">
                    {/* Neon Glow Effect */}
                    <div className="absolute inset-0 bg-gradient-to-br from-accent-neon/20 via-transparent to-electric-violet/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* Animated Border */}
                    <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-accent-neon via-electric-violet to-cool-blue p-[2px] opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="h-full w-full bg-gray-900 rounded-3xl"></div>
                    </div>

                    {/* Content */}
                    <div className="relative z-10 h-full flex flex-col">
                      {/* Lottie Animation */}
                      <motion.div
                        className="mb-6 flex justify-center"
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Player
                          autoplay
                          loop
                          src={service.lottieUrl}
                          style={{ width: '80px', height: '80px' }}
                        />
                      </motion.div>

                      {/* Service Title */}
                      <motion.h3
                        className="text-2xl md:text-3xl font-bold mb-3 font-orbitron text-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-accent-neon group-hover:to-electric-violet transition-all duration-300"
                        whileHover={{ scale: 1.05 }}
                      >
                        {service.title}
                      </motion.h3>

                      {/* Subtitle Badge */}
                      <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-accent-neon/20 to-electric-violet/20 rounded-full border border-white/20 mb-4 w-fit">
                        <span className="text-sm font-medium text-white">{service.subtitle}</span>
                      </div>

                      {/* Description */}
                      <p className="text-gray-300 mb-8 flex-grow leading-relaxed">
                        {service.description}
                      </p>

                      {/* CTA Button */}
                      <motion.div
                        className="flex items-center justify-between mt-auto"
                        whileHover={{ x: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        <span className="text-white/80 font-medium">Explore Service</span>
                        <div className="w-10 h-10 bg-gradient-to-r from-accent-neon to-electric-violet rounded-full flex items-center justify-center group-hover:shadow-lg group-hover:shadow-accent-neon/50 transition-all duration-300">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                          </svg>
                        </div>
                      </motion.div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Our Services - Enhanced with Framer Motion */}
      <section className="py-20 bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-grid-pattern"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 font-orbitron text-white">
              Why Choose <span className="text-transparent bg-clip-text bg-gradient-to-r from-accent-neon to-electric-violet">Orivox</span> Services
            </h2>
            <p className="text-xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
              Our multi-domain approach creates unique synergies and opportunities across different technology sectors.
            </p>
          </motion.div>

          {/* Features Grid with Staggered Animation */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.2
                }
              }
            }}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {[
              {
                title: "Integrated Innovation",
                description: "Our services complement each other, creating a holistic ecosystem of future-focused solutions.",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                ),
                gradient: "from-yellow-400 to-orange-500"
              },
              {
                title: "Cutting-Edge Technology",
                description: "We're constantly pushing boundaries in display technology, student empowerment, and scientific exploration.",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                ),
                gradient: "from-accent-neon to-cool-blue"
              },
              {
                title: "Future-Focused Solutions",
                description: "Our services are designed with scalability and long-term impact in mind, anticipating tomorrow's challenges.",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                  </svg>
                ),
                gradient: "from-electric-violet to-purple-600"
              },
              {
                title: "Cross-Domain Expertise",
                description: "We bring diverse knowledge and skills together, creating unique perspectives and innovative approaches.",
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                ),
                gradient: "from-green-400 to-blue-500"
              }
            ].map((item, index) => (
              <motion.div
                key={item.title}
                className="group relative"
                variants={{
                  hidden: { opacity: 0, y: 50, scale: 0.9 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    scale: 1,
                    transition: { duration: 0.6, ease: "easeOut" }
                  }
                }}
                whileHover={{
                  y: -8,
                  transition: { duration: 0.3 }
                }}
              >
                {/* Glassmorphism Card */}
                <div className="relative bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8 h-full group-hover:bg-white/10 transition-all duration-500">
                  {/* Gradient Border on Hover */}
                  <div className={`absolute inset-0 bg-gradient-to-r ${item.gradient} rounded-2xl p-[1px] opacity-0 group-hover:opacity-100 transition-opacity duration-500`}>
                    <div className="h-full w-full bg-gray-800 rounded-2xl"></div>
                  </div>

                  <div className="relative z-10 flex items-start space-x-4">
                    {/* Icon with Gradient Background */}
                    <motion.div
                      className={`flex-shrink-0 w-16 h-16 bg-gradient-to-r ${item.gradient} rounded-xl flex items-center justify-center text-white shadow-lg`}
                      whileHover={{
                        scale: 1.1,
                        rotate: 5,
                        transition: { duration: 0.3 }
                      }}
                    >
                      {item.icon}
                    </motion.div>

                    <div className="flex-1">
                      <motion.h3
                        className="text-2xl font-bold mb-3 text-white font-orbitron"
                        whileHover={{
                          scale: 1.02,
                          transition: { duration: 0.2 }
                        }}
                      >
                        {item.title}
                      </motion.h3>
                      <p className="text-gray-300 leading-relaxed">
                        {item.description}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section - Enhanced with Lottie and Advanced Animations */}
      <section className="relative py-20 bg-gradient-to-br from-indigo-900 via-purple-900 to-gray-900 overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-accent-neon/10 via-transparent to-electric-violet/10"></div>
          <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        </div>

        {/* Floating Lottie Elements */}
        <motion.div
          className="absolute top-10 right-10 opacity-30"
          animate={{
            y: [0, -20, 0],
            rotate: [0, 5, 0]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Player
            autoplay
            loop
            src="https://lottie.host/embed/floating-elements.json"
            style={{ width: '100px', height: '100px' }}
          />
        </motion.div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 40, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {/* Main CTA Lottie */}
            <motion.div
              className="mb-8 flex justify-center"
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.3 }}
            >
              <Player
                autoplay
                loop
                src="https://lottie.host/embed/cta-rocket.json"
                style={{ width: '150px', height: '150px' }}
              />
            </motion.div>

            <motion.h2
              className="text-4xl md:text-6xl font-bold mb-6 font-orbitron text-transparent bg-clip-text bg-gradient-to-r from-accent-neon via-electric-violet to-cool-blue"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              Ready to Experience the Future?
            </motion.h2>

            <motion.p
              className="text-xl md:text-2xl max-w-3xl mx-auto mb-12 text-gray-300 leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Discover how our innovative services can transform your ideas into reality.
            </motion.p>

            {/* Enhanced CTA Button */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              <Link
                to="/about"
                className="group relative inline-flex items-center px-12 py-4 bg-gradient-to-r from-accent-neon to-electric-violet rounded-full text-white font-bold text-lg shadow-2xl hover:shadow-accent-neon/50 transition-all duration-300 overflow-hidden"
              >
                {/* Button Background Animation */}
                <div className="absolute inset-0 bg-gradient-to-r from-electric-violet to-accent-neon opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <span className="relative z-10 mr-3">Learn More About Orivox</span>

                {/* Animated Arrow */}
                <motion.svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 relative z-10"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </motion.svg>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Floating Contact Button */}
      <FloatingContactButton />

      {/* Enhanced CSS Styles */}
      <style jsx>{`
        .bg-grid-pattern {
          background-image:
            linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
          background-size: 30px 30px;
        }

        /* Glassmorphism utilities */
        .glass-card {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }

        /* Neon glow effects */
        .neon-glow {
          box-shadow:
            0 0 5px currentColor,
            0 0 10px currentColor,
            0 0 15px currentColor,
            0 0 20px currentColor;
        }

        /* Custom scrollbar for webkit browsers */
        ::-webkit-scrollbar {
          width: 8px;
        }

        ::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
        }

        ::-webkit-scrollbar-thumb {
          background: linear-gradient(to bottom, #00f0ff, #8A2BE2);
          border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(to bottom, #8A2BE2, #4169E1);
        }
      `}</style>
    </div>
  );
};

export default ServicesPage; 