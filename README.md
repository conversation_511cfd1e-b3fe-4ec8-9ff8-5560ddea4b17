# Orivox Website

A modern, interactive website for Orivox - a futuristic multi-domain startup focusing on Transparent Display Technology, Script&Style Freelancing, and Healthcare & Space Innovations.

## Features

- Modern, responsive design using DaisyUI and Tailwind CSS
- Smooth animations with GSAP and Framer Motion
- Interactive components and transitions
- Mobile-first approach
- Light theme with beautiful gradients

## Technologies Used

- React 18
- TypeScript
- Tailwind CSS
- DaisyUI
- GSAP
- Framer Motion
- Lottie Animations
- React Router DOM

## Getting Started

1. Clone the repository:
\`\`\`bash
git clone https://github.com/yourusername/orivox.git
cd orivox
\`\`\`

2. Install dependencies:
\`\`\`bash
npm install
\`\`\`

3. Start the development server:
\`\`\`bash
npm start
\`\`\`

4. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Project Structure

- `/src/components` - React components
- `/src/styles` - CSS and styling files
- `/public` - Static assets

## Available Scripts

- `npm start` - Runs the app in development mode
- `npm test` - Launches the test runner
- `npm run build` - Builds the app for production
- `npm run eject` - Ejects from Create React App

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

Your Name - [@yourusername](https://twitter.com/yourusername)
Project Link: [https://github.com/yourusername/orivox](https://github.com/yourusername/orivox)
